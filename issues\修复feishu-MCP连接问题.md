# 修复feishu MCP连接问题

## 问题描述
- feishu MCP服务器连接失败
- 错误：MODULE_NOT_FOUND，缺少get-intrinsic模块
- PowerShell语法兼容问题

## 解决方案
方案1：清理缓存并重新安装 → 改为方案2：全局安装

## 执行计划
1. 清理npm缓存
2. 清理npx缓存  
3. 验证Node.js和npm环境
4. 重新安装lark-mcp包
5. 验证MCP配置文件
6. 测试MCP连接
7. 重启Cursor IDE

## 当前配置（已修改）
```json
{"mcpServers":{"feishu":{"command":"lark-mcp","args":["mcp","-a","cli_a8ab0d833265901c","-s","2GFQVEF9X346qmuBjSbeqdQOWp5MvaEG","-u","u-fAgosru_F038rbffWwqavO0k5oTxg48Piy00l10wGHzd"]}}}
```

## 执行状态
- [x] 创建任务记录
- [x] 步骤1：清理npm缓存
- [x] 步骤2：清理npx缓存（部分）
- [x] 步骤3：验证环境（Node.js v22.11.0, npm 10.9.0）
- [x] 步骤4：全局安装lark-mcp (v0.3.1)
- [x] 步骤5：修改MCP配置文件
- [x] 步骤6：测试连接成功
- [ ] 步骤7：重启Cursor IDE

## 解决方案总结
- **问题根因**：npx缓存损坏，@larksuiteoapi/lark-mcp包依赖冲突
- **解决方法**：改用全局安装 `npm install -g @larksuiteoapi/lark-mcp`
- **配置修改**：将command从"npx"改为"lark-mcp"，移除"-y"和"@larksuiteoapi/lark-mcp"参数 