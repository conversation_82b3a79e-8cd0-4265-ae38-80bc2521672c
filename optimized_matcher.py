import json
import re
import logging
from difflib import SequenceMatcher

# 配置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def similar(a, b):
    """计算两个字符串之间的相似度"""
    if a == b:
        return 1.0
    if not a or not b:
        return 0.0
    a_str = str(a)[:100]
    b_str = str(b)[:100]
    return SequenceMatcher(None, a_str, b_str).ratio()

def extract_numeric_value(text):
    """从文本中提取数值，支持功率、光效等数值字段"""
    if not text:
        return None
    
    # 匹配各种数值格式
    patterns = [
        r'(\d+(?:\.\d+)?)\s*W/M',  # 功率：4W/M
        r'(\d+(?:\.\d+)?)\s*W',    # 功率：4W
        r'(\d+(?:\.\d+)?)\s*lm/W', # 光效：70lm/W
        r'≥\s*(\d+(?:\.\d+)?)',    # ≥70lm/W
        r'(\d+(?:\.\d+)?)',        # 纯数字
    ]
    
    for pattern in patterns:
        match = re.search(pattern, str(text))
        if match:
            return float(match.group(1))
    
    # 如果上面的模式都没匹配到，尝试直接提取数字
    digits_only = re.search(r'(\d+(?:\.\d+)?)', str(text))
    if digits_only:
        return float(digits_only.group(1))
    
    return None

def extract_temperature_values(text):
    """提取色温值，支持多个色温"""
    if not text:
        return []
    
    # 清理文本，移除多余空格
    text = str(text).strip()
    
    # 匹配色温格式：3000K, 4000K等，支持多种分隔符
    pattern = r'(\d+)[Kk]'
    matches = re.findall(pattern, text)
    
    # 转换为整数列表
    temp_values = [int(match) for match in matches]
    
    logger.info(f"从文本 '{text}' 中提取到色温值: {temp_values}")
    return temp_values

def parse_search_criteria(text):
    """解析搜索条件文本，提取键值对"""
    if not text or text.strip() == "":
        logger.warning("搜索条件为空")
        return {}
    
    # 清理文本
    text = text.replace('\n', ' ').strip()
    
    # 定义字段映射关系
    field_mapping = {
        '功率': ['功率', 'power'],
        '光效等级': ['光效', '整灯光效', 'luminous_efficacy'],
        '光源类型': ['光源类型', 'light_source_type'],
        '灯珠颗数': ['灯珠颗数', 'led_count'],
        '色温': ['色温', '灯具色温', 'color_temperature'],
        '显色指数': ['显色指数', 'cri'],
        '色容差': ['色容差', 'color_tolerance'],
        'IP防护等级': ['IP_防护等级', 'IP防护等级', 'ip_rating'],
        '安装方式': ['安装方式', 'installation_method'],
        '灯体材质': ['灯体材质', 'material'],
        '防眩系数': ['防眩系数', 'glare_coefficient'],
        '频闪等级': ['有无频闪', '频闪', 'flicker'],
        '防蓝光危害': ['防蓝光危害', 'blue_light_hazard'],
        '光源品牌': ['光源品牌', 'light_source_brand'],
        '驱动电源品牌': ['驱动电源品牌', 'driver_brand'],
        '产品寿命': ['产品寿命', 'lifespan'],
        '物料号': ['物料号', 'material_number'],
        '灯具尺寸': ['灯具尺寸', '产品尺寸', '外形尺寸', 'dimensions'],
        '外观颜色': ['外观颜色', '颜色', '灯体颜色', '产品颜色', 'color']  # 添加外观颜色字段
    }
    
    criteria = {}
    
    # 检查是否直接包含物料号
    material_number_pattern = r'(\d{11})'  # 假设物料号是11位数字
    material_matches = re.findall(material_number_pattern, text)
    if material_matches:
        criteria['物料号'] = material_matches[0]
        logger.info(f"直接从搜索条件中提取到物料号: {criteria['物料号']}")
    
    # 使用正则表达式提取键值对
    pattern = r'([^：:]+?)[：:]([^：:]*?)(?=\s+[^：:]*?[：:]|$)'
    matches = re.findall(pattern, text)
    
    logger.info(f"提取到的原始键值对: {matches}")
    
    for key, value in matches:
        key = key.strip()
        value = value.strip()
        
        if key and value:
            # 查找标准字段名
            standard_key = None
            for std_key, aliases in field_mapping.items():
                if key in aliases or any(similar(key, alias) > 0.8 for alias in aliases):
                    standard_key = std_key
                    break
            
            if not standard_key:
                standard_key = key
            
            criteria[standard_key] = value
    
    # 特殊处理：检查是否包含灯具尺寸信息但未被正确提取
    # 更新尺寸匹配模式，支持mm单位和更多格式
    size_pattern = r'Φ\d+(?:\.\d+)?(?:\*\d+(?:\.\d+)?)?(?:mm)?'
    size_matches = re.findall(size_pattern, text)
    if size_matches and '灯具尺寸' not in criteria:
        for size_match in size_matches:
            # 检查是否已经作为开孔尺寸被提取
            if '开孔尺寸' in criteria and size_match in criteria['开孔尺寸']:
                continue
            # 否则可能是灯具尺寸
            criteria['灯具尺寸'] = size_match
            logger.info(f"从文本中提取到灯具尺寸: {size_match}")
            break
    
    # 特殊处理：检查是否包含颜色信息但未被正确提取
    color_keywords = ['白色', '黑色', '银色', '灰色', '金色', '黄色', '红色', '蓝色', '绿色']
    for color in color_keywords:
        if color in text and '外观颜色' not in criteria:
            criteria['外观颜色'] = color
            logger.info(f"从文本中提取到外观颜色: {color}")
            break
    
    # 特殊处理：检查是否包含功率信息但未被正确提取
    power_pattern = r'(\d+(?:\.\d+)?\s*W)'
    power_matches = re.findall(power_pattern, text)
    if power_matches and '功率' not in criteria:
        criteria['功率'] = power_matches[0].strip()
        logger.info(f"从文本中提取到功率: {criteria['功率']}")
    
    logger.info(f"标准化后的搜索条件: {criteria}")
    return criteria

def match_power_value(criteria_power, product_power):
    """专门用于功率匹配的函数"""
    if not criteria_power or not product_power:
        return 0
    
    logger.info(f"匹配功率: 条件='{criteria_power}', 产品='{product_power}'")
    
    # 提取数值
    criteria_num = extract_numeric_value(criteria_power)
    product_num = extract_numeric_value(product_power)
    
    if criteria_num is None or product_num is None:
        # 如果无法提取数值，使用字符串相似度
        similarity = similar(str(criteria_power), str(product_power))
        logger.info(f"  无法提取功率数值，使用字符串相似度: {similarity:.2f}")
        return 0.5 if similarity > 0.7 else 0
    
    # 计算数值差异
    diff = abs(criteria_num - product_num)
    
    # 精确匹配
    if diff == 0:
        logger.info(f"  功率精确匹配: {criteria_num}W")
        return 1.0
    
    # 根据差异程度给分
    if diff <= 1:  # 差异在1W以内，提高精确度
        logger.info(f"  功率接近匹配(差异≤1W): {criteria_num}W vs {product_num}W")
        return 0.95
    elif diff <= 2:  # 差异在2W以内
        logger.info(f"  功率较好匹配(差异≤2W): {criteria_num}W vs {product_num}W")
        return 0.85
    elif diff <= 5:  # 差异在5W以内
        logger.info(f"  功率一般匹配(差异≤5W): {criteria_num}W vs {product_num}W")
        return 0.6
    elif diff <= 10:  # 差异在10W以内
        logger.info(f"  功率弱匹配(差异≤10W): {criteria_num}W vs {product_num}W")
        return 0.4
    else:
        logger.info(f"  功率差异较大(差异>{diff}W): {criteria_num}W vs {product_num}W")
        return 0.1

def match_temperature_value(criteria_temp, product_temp):
    """专门用于色温匹配的函数"""
    if not criteria_temp or not product_temp:
        return 0
    
    logger.info(f"匹配色温: 条件='{criteria_temp}', 产品='{product_temp}'")
    
    # 方法1: 直接字符串比较
    if str(criteria_temp).strip().lower() == str(product_temp).strip().lower():
        logger.info(f"  色温字符串精确匹配: '{criteria_temp}' == '{product_temp}'")
        return 1.0
    
    # 方法2: 提取色温值列表
    criteria_temps = extract_temperature_values(criteria_temp)
    product_temps = extract_temperature_values(product_temp)
    
    if not criteria_temps:
        logger.info(f"  无法从条件中提取色温值")
        return 0.2
    
    if not product_temps:
        logger.info(f"  无法从产品中提取色温值")
        return 0.2
    
    # 检查是否有精确匹配的色温
    for p_temp in product_temps:
        for c_temp in criteria_temps:
            if p_temp == c_temp:
                logger.info(f"  色温精确匹配: {p_temp}K")
                return 1.0
    
    # 如果没有精确匹配，检查是否有接近的色温
    best_match_diff = float('inf')
    for p_temp in product_temps:
        for c_temp in criteria_temps:
            diff = abs(p_temp - c_temp)
            if diff < best_match_diff:
                best_match_diff = diff
    
    # 根据最接近的差异给分
    if best_match_diff <= 300:  # 300K以内认为更接近
        logger.info(f"  色温接近匹配(差异≤300K): 差异={best_match_diff}K")
        return 0.8
    elif best_match_diff <= 500:  # 500K以内认为接近
        logger.info(f"  色温较好匹配(差异≤500K): 差异={best_match_diff}K")
        return 0.6
    elif best_match_diff <= 1000:  # 1000K以内认为有一定相关性
        logger.info(f"  色温一般匹配(差异≤1000K): 差异={best_match_diff}K")
        return 0.4
    else:
        logger.info(f"  色温差异较大(差异>{best_match_diff}K)")
        return 0.2

def match_criterion(criterion_value, product_value, field_name=None):
    """改进的值匹配函数，根据字段类型使用不同的匹配策略"""
    if not criterion_value or not product_value:
        return 0
    
    # 处理"/"值（表示无此参数）
    if str(product_value).strip() == "/" or str(criterion_value).strip() == "/":
        return 0
    
    # 精确匹配
    if str(criterion_value).strip() == str(product_value).strip():
        return 1.0
    
    # 根据字段类型使用专门的匹配函数
    if field_name == '功率':
        return match_power_value(criterion_value, product_value)
    elif field_name == '色温':
        return match_temperature_value(criterion_value, product_value)
    elif field_name == '开孔尺寸':
        return match_size_value(criterion_value, product_value)
    elif field_name == '灯具尺寸':
        return match_fixture_size(criterion_value, product_value)
    elif field_name == '光束角':
        return match_angle_value(criterion_value, product_value)
    elif field_name == '灯体材质':
        return match_material(criterion_value, product_value)
    elif field_name == '外观颜色':
        return match_color(criterion_value, product_value)
    elif field_name in ['光效等级', '光效', '整灯光效']:
        # 光效匹配
        criteria_num = extract_numeric_value(criterion_value)
        product_num = extract_numeric_value(product_value)
        if criteria_num and product_num:
            diff = abs(criteria_num - product_num)
            if diff == 0:
                return 1.0
            elif diff <= 10:
                return 0.8
            elif diff <= 20:
                return 0.6
            else:
                return 0.3
    elif field_name == '显色指数':
        # 显色指数匹配
        if 'Ra≥90' in str(criterion_value) and 'Ra≥90' in str(product_value):
            return 1.0
        elif 'Ra≥' in str(criterion_value) and 'Ra≥' in str(product_value):
            c_val = re.search(r'Ra≥(\d+)', str(criterion_value))
            p_val = re.search(r'Ra≥(\d+)', str(product_value))
            if c_val and p_val:
                c_num = int(c_val.group(1))
                p_num = int(p_val.group(1))
                if p_num >= c_num:
                    return 1.0
                elif p_num >= c_num - 10:
                    return 0.8
        return 0.5
    elif field_name == '光源品牌':
        # 光源品牌匹配
        brands = ['三安', '晶元', '聚灿', 'SANAN', 'FOCUS']
        for brand in brands:
            if brand in str(product_value):
                if brand in str(criterion_value) or '同档次品牌' in str(criterion_value):
                    return 1.0
        return 0.3
    
    # 检查多选项匹配（如"3000K/4000K/6500K"）
    criterion_options = str(criterion_value).split('/')
    product_options = str(product_value).split('/')
    
    for c_opt in criterion_options:
        for p_opt in product_options:
            c_opt = c_opt.strip()
            p_opt = p_opt.strip()
            if c_opt == p_opt:
                return 1.0
            if similar(c_opt, p_opt) > 0.8:
                return 0.8
    
    # 包含关系检查
    if str(criterion_value).lower() in str(product_value).lower() or \
       str(product_value).lower() in str(criterion_value).lower():
        return 0.7
    
    # 字符串相似度
    similarity = similar(str(criterion_value), str(product_value))
    if similarity > 0.8:
        return 0.6
    elif similarity > 0.6:
        return 0.4
    
    return 0

def find_matching_field(criteria_field, product_fields):
    """在产品字段中找到最匹配的字段名"""
    best_match = None
    best_score = 0
    
    # 字段名映射
    field_aliases = {
        '功率': ['功率', 'power'],
        '光效等级': ['光效', '整灯光效', 'luminous_efficacy'],
        '色温': ['色温', '灯具色温', 'color_temperature'],
        'IP防护等级': ['IP_防护等级', 'IP防护等级', 'ip_rating'],
        '光源品牌': ['光源品牌', 'light_source_brand'],
        '光源类型': ['光源类型', 'light_source_type'],
        '显色指数': ['显色指数', 'cri'],
        '安装方式': ['安装方式', 'installation_method'],
        '灯体材质': ['灯体材质', 'material'],
        '有无频闪': ['有无频闪', '频闪等级', 'flicker'],
        '物料号': ['物料号', 'material_number'],
        '灯具尺寸': ['灯具尺寸', '产品尺寸', '外形尺寸', 'dimensions', '尺寸'],
        '外观颜色': ['外观颜色', '颜色', '灯体颜色', '产品颜色', 'color']  # 添加外观颜色的别名
    }
    
    # 首先检查直接映射
    if criteria_field in field_aliases:
        for alias in field_aliases[criteria_field]:
            if alias in product_fields:
                return alias, 1.0
    
    # 然后检查相似度
    for product_field in product_fields:
        score = similar(criteria_field, product_field)
        if score > best_score and score > 0.7:
            best_score = score
            best_match = product_field
    
    return best_match, best_score

def calculate_product_score(criteria, product):
    """计算产品与搜索条件的匹配分数"""
    total_score = 0
    matched_fields = 0
    
    logger.info(f"计算产品匹配分数，物料号: {product.get('物料号', 'N/A')}")
    
    # 定义关键字段及其权重
    key_fields = {
        '功率': 30.0,       # 极大提高功率的权重，使其成为最重要的匹配条件
        '光束角': 2.0,
        '色温': 4.0,        # 提高色温的权重
        '开孔尺寸': 3.5,     # 提高开孔尺寸的权重
        '灯具尺寸': 3.5,     # 提高灯具尺寸的权重
        '产品类别': 1.5,
        '光源类型': 2.0,     # 提高光源类型的权重
        '显色指数': 2.0,     # 提高显色指数的权重
        '安装方式': 2.0,     # 提高安装方式的权重
        '灯体材质': 3.0,     # 提高灯体材质的权重
        '光源品牌': 1.5,
        '外观颜色': 2.0      # 添加外观颜色权重
    }
    
    # 特殊处理：功率 - 使用精确匹配
    if '功率' in criteria:
        criteria_power = criteria['功率']
        if '功率' in product:
            product_power = product['功率']
            
            # 使用专门的功率匹配函数
            match_score = match_power_value(criteria_power, product_power)
            
            # 功率匹配权重
            weight = key_fields.get('功率', 30.0)
            weighted_score = match_score * weight
            total_score += weighted_score
            matched_fields += weight
            
            logger.info(f"  功率匹配: '{criteria_power}' -> '{product_power}', "
                       f"匹配分数={match_score:.2f}, 权重={weight}, 加权分数={weighted_score:.2f}")
    
    # 特殊处理：检查是否有灯具尺寸字段，如果有，优先处理
    if '灯具尺寸' in criteria:
        criteria_size = criteria['灯具尺寸']
        # 尝试多个可能的字段名
        size_field_names = ['灯具尺寸', '产品尺寸', '外形尺寸', '尺寸']
        for field_name in size_field_names:
            if field_name in product:
                product_size = product[field_name]
                match_score = match_fixture_size(criteria_size, product_size)
                weight = key_fields.get('灯具尺寸', 3.5)
                weighted_score = match_score * weight
                total_score += weighted_score
                matched_fields += weight
                logger.info(f"  特殊处理灯具尺寸: '{criteria_size}' -> '{product_size}', "
                           f"匹配分数={match_score:.2f}, 权重={weight}, 加权分数={weighted_score:.2f}")
                break
    
    # 特殊处理：灯体材质
    if '灯体材质' in criteria:
        criteria_material = criteria['灯体材质']
        # 尝试多个可能的字段名
        material_field_names = ['灯体材质', '材质', 'material']
        for field_name in material_field_names:
            if field_name in product:
                product_material = product[field_name]
                match_score = match_material(criteria_material, product_material)
                weight = key_fields.get('灯体材质', 3.0)
                weighted_score = match_score * weight
                total_score += weighted_score
                matched_fields += weight
                logger.info(f"  特殊处理灯体材质: '{criteria_material}' -> '{product_material}', "
                           f"匹配分数={match_score:.2f}, 权重={weight}, 加权分数={weighted_score:.2f}")
                break
    
    # 处理其他字段
    for criteria_field, criteria_value in criteria.items():
        # 跳过已处理的字段
        if criteria_field in ['灯具尺寸', '灯体材质', '功率']:
            continue
            
        # 找到最匹配的产品字段
        matching_field, field_score = find_matching_field(criteria_field, product.keys())
        
        if matching_field and field_score > 0.7:
            product_value = product[matching_field]
            match_score = match_criterion(criteria_value, product_value, criteria_field)
            
            # 根据字段重要性加权
            weight = 1.0
            if criteria_field in key_fields:
                weight = key_fields[criteria_field]
            
            weighted_score = match_score * weight
            total_score += weighted_score
            matched_fields += weight
            
            logger.info(f"  字段 '{criteria_field}' -> '{matching_field}': "
                       f"条件值='{criteria_value}', 产品值='{product_value}', "
                       f"匹配分数={match_score:.2f}, 权重={weight}, 加权分数={weighted_score:.2f}")
    
    # 计算最终分数
    final_score = 0
    if matched_fields > 0:
        final_score = total_score / matched_fields
        logger.info(f"  最终匹配分数: {final_score:.2f} (总分={total_score:.2f}, 匹配字段权重和={matched_fields:.2f})")
    
    return final_score

def match_size_value(criteria_size, product_size):
    """专门用于尺寸匹配的函数"""
    if not criteria_size or not product_size:
        return 0
    
    # 特殊处理：如果搜索条件是Φ125mm，给予精确匹配Φ125mm的产品更高的分数
    if 'Φ125' in str(criteria_size) and 'Φ125' in str(product_size):
        return 1.0
    
    # 提取数值
    criteria_pattern = r'(\d+(?:\.\d+)?)'
    product_pattern = r'(\d+(?:\.\d+)?)'
    
    criteria_matches = re.findall(criteria_pattern, str(criteria_size))
    product_matches = re.findall(product_pattern, str(product_size))
    
    if not criteria_matches or not product_matches:
        # 如果无法提取数值，使用字符串相似度
        return 0.5 if similar(str(criteria_size), str(product_size)) > 0.7 else 0
    
    # 比较主要尺寸数值
    criteria_num = float(criteria_matches[0])
    product_num = float(product_matches[0])
    
    # 计算数值差异
    diff = abs(criteria_num - product_num)
    
    # 精确匹配
    if diff == 0:
        return 1.0
    
    # 根据差异程度给分
    if diff <= 5:  # 差异在5mm以内
        return 0.9
    elif diff <= 10:  # 差异在10mm以内
        return 0.8
    elif diff <= 20:  # 差异在20mm以内
        return 0.6
    elif diff <= 30:  # 差异在30mm以内
        return 0.4
    else:
        return 0.2

def match_angle_value(criteria_angle, product_angle):
    """专门用于光束角匹配的函数"""
    if not criteria_angle or not product_angle:
        return 0
    
    # 如果任一值为"/"，表示无此参数
    if str(criteria_angle).strip() == "/" or str(product_angle).strip() == "/":
        return 0.1  # 给予一个较低的匹配分数
    
    # 提取数值
    criteria_pattern = r'(\d+(?:\.\d+)?)'
    product_pattern = r'(\d+(?:\.\d+)?)'
    
    criteria_matches = re.findall(criteria_pattern, str(criteria_angle))
    product_matches = re.findall(product_pattern, str(product_angle))
    
    if not criteria_matches or not product_matches:
        # 如果无法提取数值，使用字符串相似度
        return 0.5 if similar(str(criteria_angle), str(product_angle)) > 0.7 else 0
    
    # 比较角度数值
    criteria_num = float(criteria_matches[0])
    product_num = float(product_matches[0])
    
    # 计算数值差异
    diff = abs(criteria_num - product_num)
    
    # 精确匹配
    if diff == 0:
        return 1.0
    
    # 根据差异程度给分
    if diff <= 5:  # 差异在5度以内
        return 0.9
    elif diff <= 10:  # 差异在10度以内
        return 0.8
    elif diff <= 15:  # 差异在15度以内
        return 0.6
    elif diff <= 20:  # 差异在20度以内
        return 0.4
    else:
        return 0.2

def match_fixture_size(criteria_size, product_size):
    """专门用于灯具尺寸匹配的函数"""
    if not criteria_size or not product_size:
        return 0
    
    logger.info(f"匹配灯具尺寸: 条件='{criteria_size}', 产品='{product_size}'")
    
    # 清理尺寸字符串，移除mm单位
    criteria_size = str(criteria_size).replace('mm', '').strip()
    product_size = str(product_size).replace('mm', '').strip()
    
    # 精确匹配检查
    if criteria_size == product_size:
        logger.info("  灯具尺寸精确匹配")
        return 1.0
    
    # 提取直径和高度
    diameter_pattern = r'Φ(\d+(?:\.\d+)?)'
    height_pattern = r'\*(\d+(?:\.\d+)?)'
    
    c_diameter = re.search(diameter_pattern, criteria_size)
    p_diameter = re.search(diameter_pattern, product_size)
    c_height = re.search(height_pattern, criteria_size)
    p_height = re.search(height_pattern, product_size)
    
    logger.info(f"  提取的尺寸参数: 条件直径={c_diameter.group(1) if c_diameter else 'None'}, "
               f"产品直径={p_diameter.group(1) if p_diameter else 'None'}, "
               f"条件高度={c_height.group(1) if c_height else 'None'}, "
               f"产品高度={p_height.group(1) if p_height else 'None'}")
    
    score = 0
    count = 0
    
    # 比较直径
    if c_diameter and p_diameter:
        c_diam = float(c_diameter.group(1))
        p_diam = float(p_diameter.group(1))
        diam_diff = abs(c_diam - p_diam)
        
        if diam_diff == 0:
            score += 1.0
            logger.info(f"  直径完全匹配: {c_diam}")
        elif diam_diff <= 5:
            score += 0.9
            logger.info(f"  直径接近匹配: {c_diam} vs {p_diam}, 差异={diam_diff}")
        elif diam_diff <= 10:
            score += 0.7
            logger.info(f"  直径部分匹配: {c_diam} vs {p_diam}, 差异={diam_diff}")
        elif diam_diff <= 20:
            score += 0.5
            logger.info(f"  直径轻微匹配: {c_diam} vs {p_diam}, 差异={diam_diff}")
        else:
            score += 0.2
            logger.info(f"  直径差异较大: {c_diam} vs {p_diam}, 差异={diam_diff}")
        
        count += 1
    
    # 比较高度
    if c_height and p_height:
        c_h = float(c_height.group(1))
        p_h = float(p_height.group(1))
        h_diff = abs(c_h - p_h)
        
        if h_diff == 0:
            score += 1.0
            logger.info(f"  高度完全匹配: {c_h}")
        elif h_diff <= 5:
            score += 0.9
            logger.info(f"  高度接近匹配: {c_h} vs {p_h}, 差异={h_diff}")
        elif h_diff <= 10:
            score += 0.7
            logger.info(f"  高度部分匹配: {c_h} vs {p_h}, 差异={h_diff}")
        elif h_diff <= 15:
            score += 0.5
            logger.info(f"  高度轻微匹配: {c_h} vs {p_h}, 差异={h_diff}")
        else:
            score += 0.2
            logger.info(f"  高度差异较大: {c_h} vs {p_h}, 差异={h_diff}")
        
        count += 1
    
    # 计算平均分数
    if count > 0:
        final_score = score / count
        logger.info(f"  灯具尺寸最终匹配分数: {final_score:.2f}")
        return final_score
    else:
        # 如果无法提取数值，使用字符串相似度
        similarity = similar(criteria_size, product_size)
        logger.info(f"  无法提取尺寸数值，使用字符串相似度: {similarity:.2f}")
        return 0.5 if similarity > 0.7 else 0

def match_material(criteria_material, product_material):
    """专门用于灯体材质匹配的函数"""
    if not criteria_material or not product_material:
        return 0
    
    # 材质关键词列表
    material_keywords = {
        '铝合金': ['铝合金', '铝', 'aluminum', 'aluminium'],
        '冷轧板': ['冷轧板', '冷轧钢板', '钢板', 'steel'],
        '静电喷涂': ['静电喷涂', '喷涂', '表面处理'],
        '纳米': ['纳米', 'nano'],
        '导热塑料': ['导热塑料', 'PC','PC+PP','PP']
    }
    
    # 精确匹配
    if str(criteria_material).strip() == str(product_material).strip():
        return 1.0
    
    # 计算关键词匹配度
    matched_keywords = 0
    total_keywords = 0
    
    for keyword, aliases in material_keywords.items():
        if any(kw in str(criteria_material).lower() for kw in aliases):
            total_keywords += 1
            if any(kw in str(product_material).lower() for kw in aliases):
                matched_keywords += 1
    
    # 如果有关键词匹配
    if total_keywords > 0:
        match_ratio = matched_keywords / total_keywords
        if match_ratio >= 0.8:
            return 0.9
        elif match_ratio >= 0.6:
            return 0.8
        elif match_ratio >= 0.4:
            return 0.6
        elif match_ratio > 0:
            return 0.4
    
    # 检查包含关系
    if '铝合金' in str(criteria_material) and '铝合金' in str(product_material):
        return 0.9
    elif '冷轧板' in str(criteria_material) and '冷轧板' in str(product_material):
        return 0.9
    elif '静电喷涂' in str(criteria_material) and '静电喷涂' in str(product_material):
        return 0.8
    
    # 特殊处理：如果搜索条件包含"铝合金/冷轧板材质，纳米级静电喷涂表面处理"
    if '铝合金' in str(criteria_material) and '冷轧板' in str(criteria_material) and '静电喷涂' in str(criteria_material):
        if ('铝' in str(product_material) or '合金' in str(product_material)) and ('喷涂' in str(product_material)):
            return 0.8
    
    # 字符串相似度
    similarity = similar(str(criteria_material), str(product_material))
    if similarity > 0.7:
        return 0.7
    elif similarity > 0.5:
        return 0.5
    
    return 0.2  # 给一个基础分，因为材质通常描述方式多样

def match_color(criteria_color, product_color):
    """专门用于外观颜色匹配的函数"""
    if not criteria_color or not product_color:
        return 0
    
    logger.info(f"匹配外观颜色: 条件='{criteria_color}', 产品='{product_color}'")
    
    # 颜色映射表，包含常见颜色及其别名
    color_mapping = {
        '白色': ['白色', '白', 'white', '乳白色', '象牙白', '纯白','砂白SNW'],
        '黑色': ['黑色', '黑', 'black', '碳黑', '亮黑','砂黑SBK'],
        '银色': ['银色', '银', 'silver', '银灰', '银白'],
        '灰色': ['灰色', '灰', 'gray', 'grey', '深灰', '浅灰'],
        '金色': ['金色', '金', 'gold', '香槟金', '玫瑰金'],
        '黄色': ['黄色', '黄', 'yellow', '明黄'],
        '红色': ['红色', '红', 'red', '深红', '暗红'],
        '蓝色': ['蓝色', '蓝', 'blue', '深蓝', '浅蓝', '天蓝'],
        '绿色': ['绿色', '绿', 'green', '深绿', '浅绿']
    }
    
    # 精确匹配
    if str(criteria_color).strip() == str(product_color).strip():
        logger.info("  外观颜色精确匹配")
        return 1.0
    
    # 标准化颜色值
    criteria_color_std = None
    product_color_std = None
    
    # 将颜色转换为标准颜色类别
    criteria_color_lower = str(criteria_color).lower()
    product_color_lower = str(product_color).lower()
    
    # 确定搜索条件的标准颜色
    for main_color, aliases in color_mapping.items():
        if any(alias.lower() in criteria_color_lower for alias in aliases):
            criteria_color_std = main_color
            break
    
    # 确定产品的标准颜色
    for main_color, aliases in color_mapping.items():
        if any(alias.lower() in product_color_lower for alias in aliases):
            product_color_std = main_color
            break
    
    logger.info(f"  标准化颜色: 条件='{criteria_color_std}', 产品='{product_color_std}'")
    
    # 如果两者都有标准颜色且相同，则为高匹配度
    if criteria_color_std and product_color_std and criteria_color_std == product_color_std:
        logger.info(f"  标准颜色匹配: {criteria_color_std}")
        return 0.9
    
    # 如果标准颜色不同，则为低匹配度或不匹配
    if criteria_color_std and product_color_std and criteria_color_std != product_color_std:
        logger.info(f"  标准颜色不匹配: {criteria_color_std} != {product_color_std}")
        return 0.1  # 返回很低的分数，因为颜色不匹配
    
    # 检查颜色描述中是否包含相同的颜色词
    for color_word in color_mapping.keys():
        if color_word in criteria_color and color_word in product_color:
            logger.info(f"  颜色词匹配: {color_word}")
            return 0.8
    
    # 检查是否有部分匹配，但避免匹配到相反的颜色
    # 例如，避免"黑色"匹配到包含"白色"的描述
    opposite_colors = {
        '白色': ['黑色', '深灰色','砂黑SBK'],
        '黑色': ['白色', '浅色','砂白SNW'],
        '红色': ['蓝色', '绿色'],
        '蓝色': ['红色', '黄色'],
        '绿色': ['红色', '紫色']
    }
    
    # 检查是否存在相反颜色
    if criteria_color_std:
        opposites = opposite_colors.get(criteria_color_std, [])
        for opp in opposites:
            if opp in product_color:
                logger.info(f"  检测到相反颜色: {criteria_color_std} vs {opp}")
                return 0.0  # 完全不匹配
    
    # 字符串相似度，但要更谨慎
    similarity = similar(criteria_color, product_color)
    logger.info(f"  颜色字符串相似度: {similarity:.2f}")
    if similarity > 0.8:
        return 0.6
    elif similarity > 0.6:
        return 0.3  # 降低相似度匹配的分数
    
    return 0.1  # 给一个很低的基础分

def filter_opposite_colors(material_numbers, products, color_criteria):
    """过滤掉与搜索颜色相反的产品"""
    opposite_colors = {
        '白色': ['黑色', '深灰色','砂黑SBK'],
        '黑色': ['白色', '浅色','砂白SNW'],
        '红色': ['蓝色', '绿色'],
        '蓝色': ['红色', '黄色'],
        '绿色': ['红色', '紫色']
    }
    
    # 确定搜索条件的标准颜色
    color_mapping = {
        '白色': ['白色', '白', 'white', '乳白色', '象牙白', '纯白','砂白SNW'],
        '黑色': ['黑色', '黑', 'black', '碳黑', '亮黑','砂黑SBK'],
        '银色': ['银色', '银', 'silver', '银灰', '银白'],
        '灰色': ['灰色', '灰', 'gray', 'grey', '深灰', '浅灰'],
        '金色': ['金色', '金', 'gold', '香槟金', '玫瑰金'],
        '黄色': ['黄色', '黄', 'yellow', '明黄'],
        '红色': ['红色', '红', 'red', '深红', '暗红'],
        '蓝色': ['蓝色', '蓝', 'blue', '深蓝', '浅蓝', '天蓝'],
        '绿色': ['绿色', '绿', 'green', '深绿', '浅绿']
    }
    
    criteria_color_lower = str(color_criteria).lower()
    criteria_color_std = None
    
    for main_color, aliases in color_mapping.items():
        if any(alias.lower() in criteria_color_lower for alias in aliases):
            criteria_color_std = main_color
            break
    
    if not criteria_color_std:
        return material_numbers  # 如果无法确定标准颜色，返回原始结果
    
    # 获取所有产品的颜色信息
    product_colors = {}
    for product in products:
        material_number = product.get('物料号', '')
        product_color = product.get('外观颜色', '') or product.get('颜色', '') or product.get('灯体颜色', '') or product.get('产品颜色', '')
        product_colors[material_number] = product_color
    
    # 过滤掉与搜索颜色相反的产品
    filtered_numbers = []
    for material_number in material_numbers:
        product_color = product_colors.get(material_number, '')
        if not any(opposite in product_color for opposite in opposite_colors.get(criteria_color_std, [])):
            filtered_numbers.append(material_number)
    
    return filtered_numbers

def main(arg1, arg):
    try:
        logger.info("开始处理产品搜索请求")
        logger.info(f"搜索条件: {arg}")
        
        # 验证输入参数
        if not arg1 or arg1.strip() == "":
            logger.error("产品数据为空")
            return json.dumps({"error": "产品数据不能为空"})
        
        if not arg or arg.strip() == "":
            logger.error("搜索条件为空")
            return json.dumps({"error": "搜索条件不能为空"})
        
        # 解析产品数据
        try:
            # 检查是否是JSON对象而不是数组
            products = []
            if arg1.strip().startswith("{"):
                # 尝试将多个JSON对象解析为数组
                try:
                    # 检查是否有多个JSON对象
                    if "},{" in arg1:
                        products_json = "[" + arg1 + "]"
                        products = json.loads(products_json)
                        logger.info("成功将多个JSON对象解析为数组")
                    else:
                        # 单个JSON对象
                        product = json.loads(arg1)
                        products = [product]
                        logger.info("成功将单个JSON对象添加到数组")
                except Exception as e:
                    logger.error(f"解析JSON对象时出错: {str(e)}")
                    try:
                        # 尝试解析为包含arg1字段的对象
                        json_data = json.loads(arg1)
                        if "arg1" in json_data and isinstance(json_data["arg1"], str):
                            products_json = json_data["arg1"]
                            products = json.loads(products_json)
                        else:
                            logger.error("无法从JSON对象中提取产品数据")
                            return json.dumps({"error": "无法从JSON对象中提取产品数据"})
                    except Exception as e2:
                        logger.error(f"尝试其他解析方法时出错: {str(e2)}")
                        return json.dumps({"error": f"无法解析产品数据: {str(e2)}"})
            else:
                products = json.loads(arg1)
            
            if not isinstance(products, list):
                logger.error("产品数据不是列表格式")
                return json.dumps({"error": "产品数据必须是JSON数组"})
            logger.info(f"成功解析产品数据，共 {len(products)} 个产品")
            
            # 调试：输出部分产品的色温
            logger.info("产品色温列表(前20个):")
            for i, product in enumerate(products[:20]):
                logger.info(f"  产品 {i+1}: 物料号={product.get('物料号', 'N/A')}, 色温={product.get('色温', 'N/A')}")
            
        except json.JSONDecodeError as e:
            logger.error(f"解析产品数据时出错: {str(e)}")
            return json.dumps({"error": f"产品数据不是有效的JSON: {str(e)}"})
        
        # 解析搜索条件
        criteria = parse_search_criteria(arg)
        if not criteria:
            logger.warning("未能提取到有效的搜索条件")
            return json.dumps({"error": "未能提取到有效的搜索条件"})
        
        # 检查是否包含功率条件
        has_power_criteria = '功率' in criteria
        power_criteria = criteria.get('功率', '')
        
        # 检查是否包含色温条件
        has_temp_criteria = '色温' in criteria
        temp_criteria = criteria.get('色温', '')
        
        # 提取功率值和色温值
        power_value = None
        power_number = None
        temp_values = []
        
        if has_power_criteria:
            # 提取带单位的功率值
            power_match = re.search(r'(\d+(?:\.\d+)?\s*W)', str(power_criteria))
            if power_match:
                power_value = power_match.group(1).strip()
                logger.info(f"从搜索条件中提取到功率值: '{power_value}'")
            
            # 提取纯数字
            number_match = re.search(r'(\d+(?:\.\d+)?)', str(power_criteria))
            if number_match:
                power_number = number_match.group(1).strip()
                logger.info(f"从搜索条件中提取到功率数字: '{power_number}'")
        
        if has_temp_criteria:
            temp_values = extract_temperature_values(temp_criteria)
            logger.info(f"从搜索条件中提取到色温值: {temp_values}")
        
        # 如果有功率条件，优先匹配功率完全相同的产品
        exact_power_matches = []
        if has_power_criteria and (power_value or power_number):
            logger.info(f"开始查找功率匹配的产品")
            for product in products:
                product_power = product.get('功率', '')
                
                # 精确匹配检查
                is_exact_match = False
                
                # 方法1: 直接字符串比较
                if power_criteria and str(power_criteria).strip().lower() == str(product_power).strip().lower():
                    is_exact_match = True
                    logger.info(f"直接字符串匹配成功: '{power_criteria}' == '{product_power}', 物料号: {product.get('物料号', 'N/A')}")
                
                # 方法2: 提取产品功率值（如"4W"）进行比较
                if not is_exact_match and power_value:
                    product_power_match = re.search(r'(\d+(?:\.\d+)?\s*W)', str(product_power))
                    if product_power_match:
                        product_power_value = product_power_match.group(1).strip()
                        
                        if power_value.lower() == product_power_value.lower():
                            is_exact_match = True
                            logger.info(f"功率值匹配成功: '{power_value}' == '{product_power_value}', 物料号: {product.get('物料号', 'N/A')}")
                
                # 方法3: 提取纯数字进行比较
                if not is_exact_match and power_number:
                    product_number_match = re.search(r'(\d+(?:\.\d+)?)', str(product_power))
                    if product_number_match:
                        product_power_number = product_number_match.group(1).strip()
                        
                        if power_number == product_power_number:
                            is_exact_match = True
                            logger.info(f"功率数字匹配成功: '{power_number}' == '{product_power_number}', 物料号: {product.get('物料号', 'N/A')}")
                
                # 如果是精确匹配，添加到结果中
                if is_exact_match:
                    exact_power_matches.append(product)
            
            logger.info(f"找到 {len(exact_power_matches)} 个功率精确匹配的产品")
        
        # 如果有色温条件，在功率匹配的基础上进一步筛选色温匹配的产品
        exact_temp_matches = []
        if has_temp_criteria and temp_values and exact_power_matches:
            logger.info(f"在功率匹配的产品中查找色温匹配的产品")
            for product in exact_power_matches:
                product_temp = product.get('色温', '')
                product_temp_values = extract_temperature_values(product_temp)
                
                # 检查是否有匹配的色温
                is_temp_match = False
                for p_temp in product_temp_values:
                    for c_temp in temp_values:
                        if p_temp == c_temp:
                            is_temp_match = True
                            logger.info(f"色温匹配成功: {p_temp}K, 物料号: {product.get('物料号', 'N/A')}")
                            break
                    if is_temp_match:
                        break
                
                # 如果色温匹配，添加到结果中
                if is_temp_match:
                    exact_temp_matches.append(product)
            
            logger.info(f"找到 {len(exact_temp_matches)} 个功率和色温都精确匹配的产品")
        
        # 确定要评分的产品集合
        if exact_temp_matches:
            # 如果有功率和色温都匹配的产品，优先使用这些产品
            products_to_score = exact_temp_matches
            logger.info("使用功率和色温都匹配的产品进行评分")
        elif exact_power_matches:
            # 如果只有功率匹配的产品，使用这些产品
            products_to_score = exact_power_matches
            logger.info("使用功率匹配的产品进行评分")
        else:
            # 如果没有匹配的产品，使用所有产品
            products_to_score = products
            logger.info("使用所有产品进行评分")
        
        # 计算每个产品的匹配分数
        product_scores = []
        
        # 检查是否包含其他条件
        has_color_criteria = '外观颜色' in criteria
        color_criteria = criteria.get('外观颜色', '')
        has_material_criteria = '灯体材质' in criteria
        material_criteria = criteria.get('灯体材质', '')
        
        for i, product in enumerate(products_to_score):
            # 计算总体匹配分数
            score = calculate_product_score(criteria, product)
            
            # 如果是功率完全匹配的产品，提高分数
            if has_power_criteria and product in exact_power_matches:
                original_score = score
                score = score * 1.5  # 提高50%的分数
                score = min(score, 1.0)  # 确保不超过1.0
                logger.info(f"产品 {i+1} 功率完全匹配，提高总分: {original_score:.2f} -> {score:.2f}, 物料号: {product.get('物料号', 'N/A')}")
            
            # 如果是色温完全匹配的产品，进一步提高分数
            if has_temp_criteria and product in exact_temp_matches:
                original_score = score
                score = score * 1.3  # 再提高30%的分数
                score = min(score, 1.0)  # 确保不超过1.0
                logger.info(f"产品 {i+1} 色温完全匹配，提高总分: {original_score:.2f} -> {score:.2f}, 物料号: {product.get('物料号', 'N/A')}")
            
            if score > 0:
                product_scores.append((product, score))
                logger.info(f"产品 {i+1} (物料号: {product.get('物料号', 'N/A')}) 最终匹配分数: {score:.2f}")
        
        # 按分数排序
        product_scores.sort(
            key=lambda x: (
                -x[1],  # 匹配分数
                -match_power_value(criteria.get('功率', ''), x[0].get('功率', '')),
                -match_temperature_value(criteria.get('色温', ''), x[0].get('色温', '')),
                -int(x[0].get('灯具尺寸', '') == criteria.get('灯具尺寸', '')),  # 尺寸完全匹配优先
                -int(x[0].get('灯体材质', '') == criteria.get('灯体材质', '')),  # 材质完全匹配优先
                -int(x[0].get('IP_防护等级', '/') != '/'),  # 优先有明确 IP 防护等级的产品
                -int(x[0].get('安装方式', '/') != '/')  # 优先有明确安装方式的产品
            )
        )
        
        # 提取结果 - 只返回物料号数组
        material_numbers = []
        
        # 如果有功率和色温都匹配的产品，确保它们排在前面
        if exact_temp_matches:
            # 将功率和色温都匹配的产品添加到结果中
            for product in exact_temp_matches[:3]:
                material_number = product.get('物料号', 'N/A')
                if material_number not in material_numbers:
                    material_numbers.append(material_number)
                    logger.info(f"添加功率和色温都匹配的产品到结果: 物料号={material_number}")
            
            # 如果已经有足够的结果，直接返回
            if len(material_numbers) >= 3:
                logger.info(f"已找到足够的功率和色温都匹配的产品，返回结果: {material_numbers}")
                return json.dumps({
                    "material_numbers": material_numbers[:3]  # 确保最多返回3个
                }, ensure_ascii=False)
        
        # 如果没有足够的功率和色温都匹配的产品，添加功率匹配的产品
        if len(material_numbers) < 3 and exact_power_matches:
            for product in exact_power_matches[:5]:  # 考虑更多产品
                material_number = product.get('物料号', 'N/A')
                if material_number not in material_numbers and len(material_numbers) < 3:
                    material_numbers.append(material_number)
                    logger.info(f"添加功率匹配产品到结果: 物料号={material_number}")
        
        # 添加其他高分产品
        for product, score in product_scores:
            material_number = product.get('物料号', 'N/A')
            if material_number not in material_numbers and len(material_numbers) < 3:
                material_numbers.append(material_number)
                logger.info(f"添加高分产品到结果: 物料号={material_number}, 分数={score:.2f}")
        
        # 如果没有找到匹配产品，但有产品数据，返回前三个产品
        if not material_numbers and len(products) > 0:
            logger.warning("没有找到高匹配度的产品，返回前三个产品作为默认结果")
            for i in range(min(3, len(products))):
                material_number = products[i].get('物料号', 'N/A')
                material_numbers.append(material_number)
                logger.info(f"  默认结果 {i+1}: 物料号={material_number}")
        
        # 过滤掉与搜索颜色相反的产品
        if has_color_criteria:
            original_numbers = material_numbers.copy()
            material_numbers = filter_opposite_colors(material_numbers, products, color_criteria)
            if original_numbers != material_numbers:
                logger.info(f"过滤相反颜色后的结果: {material_numbers}")
        
        return json.dumps({
            "material_numbers": material_numbers
        }, ensure_ascii=False)
    
    except Exception as e:
        logger.error(f"主函数中出现错误: {str(e)}")
        return json.dumps({"error": str(e)})






